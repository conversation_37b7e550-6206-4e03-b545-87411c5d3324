#Selenium模块是一个自动化测试工具，能够驱动浏览器模拟人的操作，如单击、键盘输入等。
from operator import index

from selenium import webdriver
from selenium.webdriver.common.by import By
import re
import pandas as pd
import time

data_dt=[]

#从获取的网页源代码中提取目标数据
def extract_data(html_code):
    #目标数据的正则表达式！！！网站会不断更新，请到网站代码中确认：按F12在网页右侧显示代码，在 元素/Elements 中查看！！！
    p_job = 'class="jname text-cut">(.*?)</span>'
    p_salary = 'class="sal shrink-0">(.*?)</span>'
    p_needs_city =  '<div data-v-ac6a8aa4="" class="area"><div data-v-ac6a8aa4="" class="shrink-0">(.*?)</div><!----><!----></div>'
    p_company = '<a data-v-ac6a8aa4="" href=".*?" target="_blank" title="(.*?)"'

    #利用findall()函数提取目标数据
    job = re.findall(p_job, html_code, re.S)
    salary = re.findall(p_salary, html_code, re.S)
    needs_city = re.findall(p_needs_city, html_code, re.S)
    company = re.findall(p_company, html_code, re.S)

    #将几个目标数据存入列表
    data_dt.append( {'职位名称': job, '月薪': salary, '岗位城市': needs_city, '公司名称': company})
    #用上面的字典创建一个DataFrame
    return pd.DataFrame(data_dt)
def get_pages(keyword, start, end):
    # 声明要模拟的浏览器是Chrome，请提前下载Chrome浏览器和Chrome驱动
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    browser = webdriver.Chrome(options=chrome_options)
    browser.maximize_window()

    # 通过get()函数控制浏览器发起请求，访问网址,获取源码
    url = 'https://www.51job.com/'
    browser.get(url)
    #模拟人操作浏览器，输入搜索关键词，点击搜索按钮
    browser.find_element(By.XPATH, '//*[@id="kwdselectid"]').clear()
    browser.find_element(By.XPATH, '//*[@id="kwdselectid"]').send_keys(keyword)
    browser.find_element(By.XPATH, '/html/body/div[3]/div/div[1]/div/button').click()

    #等待10秒，网页切换
    time.sleep(10)

    #定义用于存储网页内容
    page_data = []

    for page in range(start, end + 1):
        # 模拟人操作浏览器，输入搜索关键词，点击搜索按钮
        browser.find_element(By.XPATH, '//*[@id="jump_page"]').clear()
        browser.find_element(By.XPATH, '//*[@id="jump_page"]').send_keys(page)
        browser.find_element(By.XPATH, '//*[@id="app"]/div/div[2]/div/div/div[2]/div/div[2]/div/div[3]/div/div/span[3]').click()

        # 因为是循环，需要等待浏览器与服务器交互刷新数据，否则获取不到动态信息
        time.sleep(10)

        #调用extract_data函数，将提取的目标数据添加到列表中
        #print(extract_data(browser.page_source),type(extract_data(browser.page_source)),type(all_data))
        page_data.append(extract_data(browser.page_source))

    #退出浏览器
    browser.quit()

    #页面内容转成DataFrame格式
    all_data = pd.concat(page_data , ignore_index = True)

    #将DataFrame保存为Excel
    all_data.to_excel('职位.xlsx', index=False)

#执行获取网页函数
get_pages('python',1, 3)
