#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Books.toscrape.com 爬虫 - Requests版本详细注释
目标网站：http://books.toscrape.com/
学习目标：理解基础爬虫原理，为学习Selenium做准备
"""

import requests          # HTTP请求库 - 用于获取网页内容
from bs4 import BeautifulSoup  # HTML解析库 - 用于解析网页结构
import pandas as pd      # 数据处理库 - 用于数据组织和Excel保存
import time             # 时间库 - 用于延时操作
import random           # 随机数库 - 用于随机延时，避免被检测
import re               # 正则表达式库 - 用于文本匹配和提取
import os               # 操作系统接口 - 用于文件操作
from urllib.parse import urljoin, urlparse  # URL处理库 - 用于链接拼接
from datetime import datetime  # 日期时间库 - 用于时间戳
import logging          # 日志库 - 用于记录操作日志

class BookScraper:
    """图书爬虫类 - 封装所有爬虫功能"""
    
    def __init__(self):
        
        self.base_url = "http://books.toscrape.com/"  # 目标网站的根URL
        
        # 🚀 创建Session对象 - 用于保持连接，提高效率
        # Session会自动管理cookies
        self.session = requests.Session()
        
        # 📚 数据存储列表 - 用于存储爬取到的所有图书数据
        self.books_data = []
        
        # 🎭 请求头设置 - 模拟真实浏览器访问，避免被反爬虫检测
        self.headers = {
            #告诉服务器是什么浏览器
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            
            #告诉服务器我们接受什么类型的内容
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            
            #告诉服务器我们的语言偏好
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            
            #告诉服务器我们支持的压缩格式
            'Accept-Encoding': 'gzip, deflate',
            
            #保持连接活跃
            'Connection': 'keep-alive',
            
           
        }
        
        # 将请求头应用到Session对象
        self.session.headers.update(self.headers);
        
        # 将英文星级转换为数字
        # 网站使用英文单词表示星级：One=1星, Two=2星, 等等
        self.rating_map = {
            'One': 1, 'Two': 2, 'Three': 3, 'Four': 4, 'Five': 5
        }
    
    def get_page(self, url, max_retries=3):
        """
        
        参数:
            max_retries: 最大重试次数（默认3次）

        """
        # 🔄 重试机制 - 如果请求失败，会自动重试
        for attempt in range(max_retries):
            try:
                
                #  发起HTTP GET请求
                response = self.session.get(url, timeout=10)  # 10秒超时
                
                #  随机延时 - 模拟人类浏览行为，避免被反爬虫检测
                time.sleep(random.uniform(1, 3))  # 随机等待1-3秒
                
                return response  # 返回响应对象
                
            except requests.RequestException as e:
              
                # 如果是最后一次尝试，记录失败并返回None
                if attempt == max_retries - 1:
                    return None
                    

                time.sleep(random.uniform(3, 6))
        
        return None
    
    def parse_book_details(self, book_element, page_url):
        """
        从单个图书元素中提取详细信息
        
        参数:
            book_element: BeautifulSoup解析的图书元素
            page_url: 当前页面的URL（用于链接拼接）
            
        返回:
            dict: 包含图书信息的字典 
        """
        try:
            # 📖 创建空字典存储图书数据
            book_data = {}
            
            # 1️⃣ 获取书名
            # HTML结构: <h3><a title="书名">...</a></h3>
            title_element = book_element.find('h3').find('a')
            book_data['书名'] = title_element.get('title', '').strip()
            
            # 2️⃣ 获取详情页链接
            # 获取<a>标签的href属性，然后拼接完整URL
            detail_link = title_element.get('href', '')
            book_data['详情链接'] = urljoin(page_url, detail_link)
            
            # 3️⃣ 获取价格信息
            # HTML结构: <p class="price_color">£51.77</p>
            price_element = book_element.find('p', class_='price_color')
            price_text = price_element.text if price_element else '£0.00'
            
            # 🔢 使用正则表达式提取数字部分
            price_match = re.search(r'[\d.]+', price_text)
            book_data['价格'] = float(price_match.group()) if price_match else 0.0
            book_data['价格文本'] = price_text  # 保留原始文本
            
            # 4️⃣ 获取评分
            # HTML结构: <p class="star-rating Three">...</p>
            star_element = book_element.find('p', class_=re.compile(r'star-rating'))
            if star_element:
                # 获取class属性列表
                star_class = star_element.get('class', [])
                
                # 🔍 在class列表中查找评分词汇
                for cls in star_class:
                    if cls in self.rating_map:
                        book_data['评分'] = self.rating_map[cls]
                        break
                else:
                    book_data['评分'] = 0  # 如果没找到，设为0
            else:
                book_data['评分'] = 0
            
            # 5️⃣ 获取库存状态
            # HTML结构: <p class="instock availability">In stock (22 available)</p>
            stock_element = book_element.find('p', class_='instock availability')
            if stock_element:
                stock_text = stock_element.text.strip()
                book_data['库存状态'] = stock_text
                
                # 🔍 判断是否有库存（检查文本中是否包含"in stock"）
                book_data['有库存'] = 'in stock' in stock_text.lower()
            else:
                book_data['库存状态'] = '未知'
                book_data['有库存'] = False
            
            # 6️⃣ 获取图片链接
            # HTML结构: <div class="image_container"><img src="..."></div>
            img_element = book_element.find('div', class_='image_container').find('img')
            if img_element:
                img_src = img_element.get('src', '')
                # 🔗 拼接完整的图片URL
                book_data['图片链接'] = urljoin(page_url, img_src)
            else:
                book_data['图片链接'] = ''
            
            return book_data
            
        except Exception as e:
            print(f"❌ 解析书籍信息失败: {e}")
            return None
    
    def scrape_page(self, page_url):
        """
        爬取单个页面的所有书籍
        
        参数:
            page_url: 页面URL
            
        返回:
            list: 包含该页面所有图书信息的列表
        """
        
        # 📡 获取页面内容
        response = self.get_page(page_url)
        if not response:
            return []  # 如果获取失败，返回空列表
        
        # 🍲 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 🔍 查找所有书籍容器
        # 每本书都在<article class="product_pod">标签内
        books = soup.find_all('article', class_='product_pod')
        
        print(f"📚 在当前页面发现 {len(books)} 本书")
        
        # 📝 存储当前页面的图书数据
        page_books = []
        
        # 🔄 遍历每本书
        for i, book in enumerate(books, 1):
            print(f"  📖 正在解析第 {i}/{len(books)} 本书...")
            
            # 🔧 解析书籍详细信息
            book_data = self.parse_book_details(book, page_url)
            
            if book_data:
                page_books.append(book_data)
                
                # 📊 显示进度信息
                title = book_data['书名'][:30] + "..." if len(book_data['书名']) > 30 else book_data['书名']
                print(f"    ✓ {title} - {book_data['价格文本']} - {book_data['评分']}星")
        
        print(f"✅ 页面解析完成，获取 {len(page_books)} 本书的信息")
        return page_books
    
    def get_total_pages(self):
        """
        获取网站总页数
        
        返回:
            int: 总页数
        """
        
        # 📡 获取首页内容
        response = self.get_page(self.base_url)
        if not response:
            return 1  # 如果获取失败，默认1页
        
        # 🍲 解析HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 🔍 查找分页信息
        # HTML结构: <li class="current">Page 1 of 50</li>
        pager = soup.find('li', class_='current')
        
        if pager:
            # 📄 解析分页文本
            text = pager.text.strip()  # 例如: "Page 1 of 50"
            
            # 🔢 使用正则表达式提取总页数
            match = re.search(r'Page \d+ of (\d+)', text)
            if match:
                total_pages = int(match.group(1))
                print(f"📄 发现总共 {total_pages} 页")
                return total_pages
        
        print("📄 未找到分页信息，默认为1页")
        return 1
    
    def scrape_all_books(self, max_pages=None):
        """
        爬取所有页面的书籍信息 - 主要工作流程
        
        参数:
            max_pages: 限制爬取的最大页数（None表示爬取所有页面）
        """
        
        print("🚀 开始爬取图书数据...")
        
        # 📊 获取总页数
        total_pages = self.get_total_pages()
        
        # 🔢 限制页数（用于测试）
        if max_pages:
            total_pages = min(total_pages, max_pages)
            print(f"🎯 限制爬取 {total_pages} 页")
        
        # 🔄 逐页爬取
        for page_num in range(1, total_pages + 1):
            
            # 🔗 构造页面URL
            if page_num == 1:
                # 首页URL特殊处理
                page_url = self.base_url
            else:
                # 其他页面URL格式: catalogue/page-2.html
                page_url = f"{self.base_url}catalogue/page-{page_num}.html"
            
            print(f"\n📖 正在爬取第 {page_num}/{total_pages} 页")
            print(f"🔗 URL: {page_url}")
            
            # 🕷️ 爬取当前页面
            page_books = self.scrape_page(page_url)
            
            # 📝 将当前页面的数据添加到总数据中
            self.books_data.extend(page_books)
            
            # 📊 显示进度
            print(f"✅ 第 {page_num} 页完成，新增 {len(page_books)} 本书")
            print(f"📚 累计总数: {len(self.books_data)} 本书")
            
            # 😴 页面间延时 - 避免请求过于频繁
            if page_num < total_pages:
                delay = random.uniform(2, 5)
                print(f"⏰ 等待 {delay:.1f} 秒...")
                time.sleep(delay)
        
        print(f"\n🎉 爬取完成！总共获取 {len(self.books_data)} 本书的信息")
    
    def save_to_excel(self, filename=None):
        """
        保存数据到Excel文件
        
        参数:
            filename: 文件名（None时自动生成）
        """
        
        if not self.books_data:
            print("⚠️ 没有数据可保存")
            return
        
        # 📅 生成文件名
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"books_data_{timestamp}.xlsx"
        
        try:
            # 📊 创建DataFrame
            df = pd.DataFrame(self.books_data)
            
            # 📈 计算统计信息
            total_books = len(df)
            avg_price = df['价格'].mean()
            avg_rating = df['评分'].mean()
            in_stock_count = df['有库存'].sum()
            
            # 💾 保存Excel文件（多个工作表）
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                
                # 📋 主数据表
                df.to_excel(writer, sheet_name='图书数据', index=False)
                
                # 📊 统计信息表
                stats_data = {
                    '统计项目': ['总书籍数', '平均价格', '平均评分', '有库存数量', '库存率'],
                    '数值': [
                        total_books, 
                        f'{avg_price:.2f}', 
                        f'{avg_rating:.1f}', 
                        in_stock_count, 
                        f'{in_stock_count/total_books*100:.1f}%'
                    ]
                }
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
            
            print(f"💾 数据已保存到: {filename}")
            print(f"📊 统计信息: {total_books}本书, 平均价格£{avg_price:.2f}, 平均{avg_rating:.1f}星")
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
    
    def display_summary(self):
        """显示爬取结果摘要"""
        
        if not self.books_data:
            print("❌ 没有爬取到数据")
            return
        
        print("\n" + "="*60)
        print(f"📚 爬取完成！共获取 {len(self.books_data)} 本书的信息")
        print("="*60)
        
        # 📖 显示前5本书
        print("\n📖 前5本书预览:")
        for i, book in enumerate(self.books_data[:5], 1):
            print(f"{i}. {book['书名']}")
            print(f"   💰 价格: {book['价格文本']} | ⭐ 评分: {book['评分']}星 | 📦 库存: {'✓' if book['有库存'] else '✗'}")
        
        # 💰 价格统计
        prices = [book['价格'] for book in self.books_data]
        print(f"\n💰 价格统计:")
        print(f"   平均价格: £{sum(prices)/len(prices):.2f}")
        print(f"   最高价格: £{max(prices):.2f}")
        print(f"   最低价格: £{min(prices):.2f}")
        
        # ⭐ 评分统计
        ratings = [book['评分'] for book in self.books_data if book['评分'] > 0]
        if ratings:
            print(f"\n⭐ 评分统计:")
            print(f"   平均评分: {sum(ratings)/len(ratings):.1f}星")
            for star in range(1, 6):
                count = ratings.count(star)
                percentage = count/len(ratings)*100 if ratings else 0
                print(f"   {star}星: {count}本 ({percentage:.1f}%)")

def main():
    """主函数 - 程序入口"""
    
    print("🚀 Books to Scrape 爬虫程序")
    print("📖 学习目标：理解基础爬虫原理")
    print("="*50)
    
    # 💬 用户输入
    try:
        max_pages = input("请输入要爬取的页数 (直接回车爬取所有页面): ").strip()
        max_pages = int(max_pages) if max_pages.isdigit() else None
    except:
        max_pages = None
    
    # 🕷️ 创建爬虫实例
    scraper = BookScraper()
    
    try:
        # ⏱️ 开始计时
        start_time = time.time()
        
        # 🚀 开始爬取
        scraper.scrape_all_books(max_pages)
        
        # ⏱️ 结束计时
        end_time = time.time()
        
        # 📊 显示结果
        scraper.display_summary()
        
        # 💾 保存数据
        scraper.save_to_excel()
        
        print(f"\n⏱️ 总耗时: {end_time - start_time:.1f} 秒")
        print(f"📁 数据文件已保存在当前目录")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断爬取")
        if scraper.books_data:
            print("💾 正在保存已爬取的数据...")
            scraper.save_to_excel()
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        if scraper.books_data:
            print("💾 正在保存已爬取的数据...")
            scraper.save_to_excel()

if __name__ == "__main__":
    main()