#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Selenium的招聘信息爬虫
功能：模拟用户操作，搜索职位，翻页，提取数据
涉及技术：浏览器启动、元素定位、鼠标点击、键盘输入、等待机制
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import pandas as pd
import time
import random
import re
import logging
from datetime import datetime

class JobScraper:
    def __init__(self):
        self.driver = None
        self.jobs_data = []
        self.wait = None
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_driver(self):
        """
        设置Chrome浏览器驱动
        """
        self.logger.info("正在启动Chrome浏览器...")
        
        # Chrome选项配置
        chrome_options = Options()
        
        # 反检测设置
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 浏览器设置
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage") 
        chrome_options.add_argument("--disable-gpu")
        
        # 用户代理
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        
        # 如果想要无头模式（不显示浏览器窗口），取消下面的注释
        # chrome_options.add_argument("--headless")
        
        try:
            # 启动浏览器
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # 反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 设置窗口大小
            self.driver.maximize_window()
            
            # 设置等待对象
            self.wait = WebDriverWait(self.driver, 10)
            
            self.logger.info("✅ 浏览器启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 浏览器启动失败: {e}")
            return False
    
    def human_like_delay(self, min_time=1, max_time=3):
        """
        模拟人类操作的随机延时
        """
        delay = random.uniform(min_time, max_time)
        time.sleep(delay)
    
    def safe_find_element(self, by, value, timeout=10):
        """
        安全的元素查找方法
        """
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            self.logger.warning(f"未找到元素: {by}={value}")
            return None
    
    def safe_click(self, element, description="元素"):
        """
        安全的点击操作
        """
        try:
            # 滚动到元素可见
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            self.human_like_delay(0.5, 1)
            
            # 等待元素可点击
            WebDriverWait(self.driver, 5).until(EC.element_to_be_clickable(element))
            
            # 执行点击
            element.click()
            self.logger.info(f"✅ 成功点击: {description}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 点击失败 {description}: {e}")
            return False
    
    def safe_input(self, element, text, description="输入框"):
        """
        安全的输入操作 - 模拟人类打字
        """
        try:
            # 清空输入框
            element.clear()
            self.human_like_delay(0.3, 0.8)
            
            # 模拟逐字输入
            for char in text:
                element.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))  # 模拟打字速度
            
            self.logger.info(f"✅ 成功输入: {description} -> '{text}'")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 输入失败 {description}: {e}")
            return False
    
    def navigate_to_51job(self):
        """
        导航到51job首页
        """
        try:
            self.logger.info("🌐 正在访问51job网站...")
            self.driver.get("https://www.51job.com/")
            
            # 等待页面加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            self.human_like_delay(2, 4)
            
            self.logger.info("✅ 成功访问51job网站")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 访问网站失败: {e}")
            return False
    
    def search_jobs(self, keyword="Python开发", location="北京"):
        """
        搜索职位 - 模拟用户搜索操作
        """
        try:
            self.logger.info(f"🔍 开始搜索: {keyword} - {location}")
            
            # 1. 定位并输入职位关键词
            search_input = self.safe_find_element(By.ID, "kwdselectid")
            if not search_input:
                # 尝试其他可能的选择器
                search_input = self.safe_find_element(By.CSS_SELECTOR, "input[placeholder*='职位']")
            
            if search_input:
                self.safe_input(search_input, keyword, "职位搜索框")
                self.human_like_delay(1, 2)
            
            # 2. 定位并输入城市
            city_input = self.safe_find_element(By.ID, "work_position_input")
            if not city_input:
                city_input = self.safe_find_element(By.CSS_SELECTOR, "input[placeholder*='城市']")
            
            if city_input:
                # 先清空
                city_input.clear()
                self.human_like_delay(0.5, 1)
                self.safe_input(city_input, location, "城市输入框")
                self.human_like_delay(1, 2)
            
            # 3. 点击搜索按钮
            search_button = self.safe_find_element(By.CSS_SELECTOR, "button[type='submit']")
            if not search_button:
                # 尝试其他搜索按钮选择器
                search_button = self.safe_find_element(By.CLASS_NAME, "p_but")
                if not search_button:
                    search_button = self.safe_find_element(By.XPATH, "//button[contains(text(),'搜索')]")
            
            if search_button:
                self.safe_click(search_button, "搜索按钮")
                self.human_like_delay(3, 5)  # 等待搜索结果加载
            else:
                # 如果找不到搜索按钮，尝试按回车
                if search_input:
                    search_input.send_keys(Keys.RETURN)
                    self.human_like_delay(3, 5)
            
            # 4. 等待搜索结果页面加载
            try:
                self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".j_joblist, .joblist-box, .j_result")))
                self.logger.info("✅ 搜索结果页面加载完成")
                return True
            except TimeoutException:
                self.logger.warning("⚠️ 搜索结果加载超时，继续尝试...")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 搜索操作失败: {e}")
            return False
    
    def extract_job_info(self, job_element):
        """
        从职位元素中提取信息
        """
        try:
            job_data = {}
            
            # 提取职位名称
            title_elem = job_element.find_element(By.CSS_SELECTOR, ".jobname a, .t1 a, .job_name a")
            job_data['职位名称'] = title_elem.text.strip() if title_elem else "未知"
            job_data['详情链接'] = title_elem.get_attribute('href') if title_elem else ""
            
            # 提取公司名称
            try:
                company_elem = job_element.find_element(By.CSS_SELECTOR, ".company_name a, .t2 a, .companyname a")
                job_data['公司名称'] = company_elem.text.strip()
            except:
                job_data['公司名称'] = "未知"
            
            # 提取薪资
            try:
                salary_elem = job_element.find_element(By.CSS_SELECTOR, ".t4, .salary, .sal")
                job_data['薪资'] = salary_elem.text.strip()
            except:
                job_data['薪资'] = "面议"
            
            # 提取工作地点
            try:
                location_elem = job_element.find_element(By.CSS_SELECTOR, ".t3, .area, .ltype")
                job_data['工作地点'] = location_elem.text.strip()
            except:
                job_data['工作地点'] = "未知"
            
            # 提取发布时间
            try:
                time_elem = job_element.find_element(By.CSS_SELECTOR, ".t5, .time, .uptime")
                job_data['发布时间'] = time_elem.text.strip()
            except:
                job_data['发布时间'] = "未知"
            
            return job_data
            
        except Exception as e:
            self.logger.error(f"提取职位信息失败: {e}")
            return None
    
    def scrape_current_page(self):
        """
        爬取当前页面的所有职位
        """
        try:
            self.logger.info("📄 正在爬取当前页面...")
            
            # 等待职位列表加载
            self.human_like_delay(2, 4)
            
            # 尝试多种可能的职位列表选择器
            job_selectors = [
                ".j_joblist .e, .j_joblist tr",
                ".joblist-box .job-li",
                ".j_result .t1",
                ".joblist .jl_info",
                "tr[onclick*='job_detail']"
            ]
            
            jobs = []
            for selector in job_selectors:
                try:
                    jobs = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if jobs:
                        self.logger.info(f"✅ 使用选择器找到 {len(jobs)} 个职位: {selector}")
                        break
                except:
                    continue
            
            if not jobs:
                self.logger.warning("⚠️ 未找到职位列表，尝试截图调试...")
                self.driver.save_screenshot(f"debug_{int(time.time())}.png")
                return []
            
            page_jobs = []
            for i, job in enumerate(jobs, 1):
                try:
                    self.logger.info(f"  正在处理第 {i}/{len(jobs)} 个职位...")
                    
                    job_data = self.extract_job_info(job)
                    if job_data and job_data['职位名称'] != "未知":
                        page_jobs.append(job_data)
                        print(f"    ✓ {job_data['职位名称']} - {job_data['公司名称']} - {job_data['薪资']}")
                    
                    # 每处理几个职位就短暂休息
                    if i % 5 == 0:
                        self.human_like_delay(0.5, 1)
                        
                except Exception as e:
                    self.logger.warning(f"处理第{i}个职位时出错: {e}")
                    continue
            
            self.logger.info(f"✅ 当前页面爬取完成，获得 {len(page_jobs)} 个有效职位")
            return page_jobs
            
        except Exception as e:
            self.logger.error(f"❌ 爬取当前页面失败: {e}")
            return []
    
    def goto_next_page(self):
        """
        模拟点击下一页
        """
        try:
            self.logger.info("🔄 尝试翻到下一页...")
            
            # 滚动到页面底部，寻找翻页按钮
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            self.human_like_delay(1, 2)
            
            # 尝试多种下一页按钮的选择器
            next_selectors = [
                "a[aria-label='Next']",
                ".next",
                "a:contains('下一页')",
                "a:contains('next')",
                ".page_next",
                "a[onclick*='next']"
            ]
            
            next_button = None
            for selector in next_selectors:
                try:
                    if ":contains" in selector:
                        # 使用XPath处理包含文本的选择器
                        xpath = f"//a[contains(text(), '下一页') or contains(text(), 'next') or contains(text(), '>'))]"
                        next_button = self.driver.find_element(By.XPATH, xpath)
                    else:
                        next_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if next_button and next_button.is_enabled():
                        break
                except:
                    continue
            
            if next_button:
                # 滚动到按钮位置
                self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                self.human_like_delay(1, 2)
                
                # 点击下一页
                self.safe_click(next_button, "下一页按钮")
                
                # 等待页面加载
                self.human_like_delay(3, 5)
                
                return True
            else:
                self.logger.info("📄 已到达最后一页或未找到下一页按钮")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 翻页失败: {e}")
            return False
    
    def scrape_jobs(self, keyword="Python开发", location="北京", max_pages=3):
        """
        完整的职位爬取流程
        """
        try:
            # 1. 启动浏览器
            if not self.setup_driver():
                return False
            
            # 2. 访问网站
            if not self.navigate_to_51job():
                return False
            
            # 3. 搜索职位
            if not self.search_jobs(keyword, location):
                return False
            
            # 4. 逐页爬取
            current_page = 1
            while current_page <= max_pages:
                self.logger.info(f"📖 正在爬取第 {current_page}/{max_pages} 页")
                
                # 爬取当前页面
                page_jobs = self.scrape_current_page()
                self.jobs_data.extend(page_jobs)
                
                self.logger.info(f"第 {current_page} 页完成，新增 {len(page_jobs)} 个职位")
                self.logger.info(f"累计爬取: {len(self.jobs_data)} 个职位")
                
                # 如果不是最后一页，尝试翻页
                if current_page < max_pages:
                    if not self.goto_next_page():
                        self.logger.info("无法翻页，爬取结束")
                        break
                
                current_page += 1
                
                # 页面间随机延时
                self.human_like_delay(2, 4)
            
            self.logger.info(f"🎉 爬取完成！总共获得 {len(self.jobs_data)} 个职位信息")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 爬取过程出错: {e}")
            return False
        finally:
            # 保持浏览器打开一段时间以供检查
            self.logger.info("浏览器将在10秒后关闭...")
            time.sleep(10)
            self.close_driver()
    
    def close_driver(self):
        """
        关闭浏览器
        """
        if self.driver:
            self.driver.quit()
            self.logger.info("🔒 浏览器已关闭")
    
    def save_to_excel(self, filename=None):
        """
        保存数据到Excel
        """
        if not self.jobs_data:
            self.logger.warning("没有数据可保存")
            return
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"jobs_data_{timestamp}.xlsx"
        
        try:
            df = pd.DataFrame(self.jobs_data)
            df.to_excel(filename, index=False)
            self.logger.info(f"✅ 数据已保存到: {filename}")
            
            # 显示统计信息
            print("\n" + "="*60)
            print(f"📊 数据统计:")
            print(f"   总职位数: {len(df)}")
            print(f"   公司数: {df['公司名称'].nunique()}")
            print(f"   平均薪资: 需要进一步解析")
            print("="*60)
            
        except Exception as e:
            self.logger.error(f"❌ 保存失败: {e}")
    
    def display_results(self):
        """
        显示爬取结果
        """
        if not self.jobs_data:
            print("没有爬取到数据")
            return
        
        print("\n" + "="*80)
        print(f"🎯 爬取结果 - 共 {len(self.jobs_data)} 个职位")
        print("="*80)
        
        # 显示前10个职位
        for i, job in enumerate(self.jobs_data[:10], 1):
            print(f"{i:2d}. {job['职位名称']}")
            print(f"     公司: {job['公司名称']}")
            print(f"     薪资: {job['薪资']}")
            print(f"     地点: {job['工作地点']}")
            print(f"     时间: {job['发布时间']}")
            print()
        
        if len(self.jobs_data) > 10:
            print(f"... 还有 {len(self.jobs_data) - 10} 个职位")

def main():
    """
    主函数
    """
    print("🤖 Selenium自动化招聘信息爬虫")
    print("="*50)
    
    # 用户输入
    keyword = input("请输入搜索关键词 (默认: Python开发): ").strip() or "Python开发"
    location = input("请输入城市 (默认: 北京): ").strip() or "北京"
    
    try:
        max_pages = int(input("请输入爬取页数 (默认: 3): ").strip() or "3")
    except:
        max_pages = 3
    
    print(f"\n开始搜索: {keyword} - {location} (爬取{max_pages}页)")
    print("注意：浏览器会自动打开，请不要手动操作")
    print("-" * 50)
    
    # 创建爬虫实例
    scraper = JobScraper()
    
    try:
        # 开始爬取
        success = scraper.scrape_jobs(keyword, location, max_pages)
        
        if success:
            # 显示结果
            scraper.display_results()
            
            # 保存数据
            scraper.save_to_excel()
            
            print("\n🎉 爬取任务完成！")
        else:
            print("\n❌ 爬取任务失败")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断程序")
        scraper.close_driver()
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        scraper.close_driver()

if __name__ == "__main__":
    main()